document.addEventListener('DOMContentLoaded', () => {
const API_URL = window.location.origin;

    // --- STATE MANAGEMENT ---
    const state = {
        token: localStorage.getItem('jwt'),
        user: null,
        currentView: '',
        room: null,
        ws: null,
        hls: null,
    };

    // --- DOM ELEMENTS ---
    const views = {
        auth: document.getElementById('auth-view'),
        pending: document.getElementById('pending-view'),
        admin: document.getElementById('admin-view'),
        library: document.getElementById('library-view'),
        room: document.getElementById('room-view'),
    };

    const loginForm = document.getElementById('login-form');
    const registerForm = document.getElementById('register-form');
    const showRegisterLink = document.getElementById('show-register');
    const showLoginLink = document.getElementById('show-login');

    // --- API HELPER ---
    async function apiRequest(endpoint, method = 'GET', body = null, overrideToken = null) {
        const headers = { 'Content-Type': 'application/json' };
        const token = overrideToken || state.token;
        if (token) {
            headers['Authorization'] = `Bearer ${token}`;
        }

        const options = {
            method,
            headers,
        };

        if (body) {
            options.body = JSON.stringify(body);
        }

        try {
            const response = await fetch(API_URL + endpoint, options);
            if (!response.ok) {
                const errorData = await response.json().catch(() => ({ message: response.statusText }));
                throw new Error(errorData.message || 'API request failed');
            }
            if (response.status === 204) {
                return null;
            }
            return await response.json();
        } catch (error) {
            console.error(`API Error (${method} ${endpoint}):`, error);
            alert(`Error: ${error.message}`);
            throw error;
        }
    }

    // --- ROUTER ---
    function navigate(viewName, params = {}) {
        state.currentView = viewName;
        Object.values(views).forEach(view => view.classList.add('hidden'));
        if (views[viewName]) {
            views[viewName].classList.remove('hidden');
        }
        
        // Update URL hash
        window.location.hash = viewName + (params.id ? `/${params.id}` : '');

        // Render view-specific content
        switch (viewName) {
            case 'admin':
                renderAdminView();
                break;
            case 'library':
                renderLibraryView();
                break;
            case 'room':
                if (params.id) {
                    joinRoom(params.id);
                }
                break;
        }
    }

    // --- AUTHENTICATION ---
    async function handleLogin(e) {
        e.preventDefault();
        const formData = new FormData(loginForm);
        const data = Object.fromEntries(formData.entries());
        try {
            const result = await apiRequest('/login', 'POST', data);
            state.token = result.token;
            localStorage.setItem('jwt', result.token);
            await fetchCurrentUser(result.token);
            initializeApp();
        } catch (error) {
            // Error is already logged by apiRequest
        }
    }

    async function handleRegister(e) {
        e.preventDefault();
        const formData = new FormData(registerForm);
        const data = Object.fromEntries(formData.entries());
        try {
            await apiRequest('/register', 'POST', data);
            alert('Registration successful! Please log in.');
            showLogin();
        } catch (error) {
            // Error is already logged by apiRequest
        }
    }

    function logout() {
        state.token = null;
        state.user = null;
        localStorage.removeItem('jwt');
        if (state.ws) {
            state.ws.close();
        }
        navigate('auth');
    }

    async function fetchCurrentUser(token = null) {
        const activeToken = token || state.token;
        if (!activeToken) return;
        try {
            state.user = await apiRequest('/api/users/me', 'GET', null, activeToken);
        } catch (error) {
            console.error("Failed to fetch current user, logging out.", error);
            logout();
        }
    }

    function showRegister() {
        loginForm.classList.add('hidden');
        registerForm.classList.remove('hidden');
    }

    function showLogin() {
        registerForm.classList.add('hidden');
        loginForm.classList.remove('hidden');
    }

    // --- VIEW RENDERERS ---
    async function renderAdminView() {
        const userList = document.getElementById('user-list');
        userList.innerHTML = '<p>Loading users...</p>';
        try {
            const users = await apiRequest('/api/admin/users');
            userList.innerHTML = users.map(user => `
                <div class="flex justify-between items-center p-3 bg-gray-800 rounded">
                    <div>
                        <p class="font-semibold">${user.username}</p>
                        <p class="text-sm text-gray-400">Verified: ${user.is_verified}</p>
                    </div>
                    ${!user.is_verified ? `
                    <div>
                        <button data-user-id="${user.id}" class="approve-btn bg-green-600 hover:bg-green-700 p-2 rounded mr-2">Approve</button>
                        <button data-user-id="${user.id}" class="deny-btn bg-red-600 hover:bg-red-700 p-2 rounded">Deny</button>
                    </div>
                    ` : ''}
                </div>
            `).join('');
        } catch (error) {
            userList.innerHTML = '<p class="text-red-500">Failed to load users.</p>';
        }
    }

    async function renderLibraryView() {
        const videoGrid = document.getElementById('video-grid');
        videoGrid.innerHTML = '<p>Loading videos...</p>';
        try {
            const videos = await apiRequest('/api/library');
            videoGrid.innerHTML = videos.map(video => `
                <div class="bg-gray-800 rounded-lg overflow-hidden cursor-pointer" data-video-id="${video.id}">
                    <img src="${video.thumbnail_path || 'https://placehold.co/400x225/000000/FFFFFF/png?text=No+Thumbnail'}" alt="${video.title}" class="w-full h-48 object-cover">
                    <div class="p-4">
                        <h3 class="font-semibold">${video.title}</h3>
                    </div>
                </div>
            `).join('');
        } catch (error) {
            videoGrid.innerHTML = '<p class="text-red-500">Failed to load videos.</p>';
        }
    }

    // --- ROOM LOGIC ---
    async function createRoom(videoId) {
        try {
            const room = await apiRequest('/api/rooms', 'POST', { videoId: videoId });
            navigate('room', { id: room.id });
        } catch (error) {
            alert('Failed to create room.');
        }
    }
    
    async function joinRoom(roomId) {
        try {
            state.room = await apiRequest(`/api/rooms/${roomId}`);
            setupWebSocket(roomId);
            setupVideoPlayer();
            document.getElementById('room-title').textContent = state.room.video.title;
        } catch (error) {
            alert('Failed to join room. It may not exist.');
            navigate('library');
        }
    }

    function leaveRoom() {
        if (state.ws) {
            state.ws.close();
            state.ws = null;
        }
        if (state.hls) {
            state.hls.destroy();
            state.hls = null;
        }
        state.room = null;
        navigate('library');
    }

    function setupVideoPlayer() {
        const video = document.getElementById('video-player');
        const videoUrl = `${API_URL}/hls/${state.room.video.id}/master.m3u8`;

        if (Hls.isSupported()) {
            if (state.hls) state.hls.destroy();
            state.hls = new Hls();
            state.hls.loadSource(videoUrl);
            state.hls.attachMedia(video);
        } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
            video.src = videoUrl;
        }

        // Event listeners to send control messages
        video.addEventListener('play', () => sendWsMessage({ type: 'control_play', data: { current_time: video.currentTime } }));
        video.addEventListener('pause', () => sendWsMessage({ type: 'control_pause', data: { current_time: video.currentTime } }));
        video.addEventListener('seeked', () => sendWsMessage({ type: 'control_seek', data: { current_time: video.currentTime } }));
    }

    // --- WEBSOCKET LOGIC ---
    function setupWebSocket(roomId) {
        const wsProtocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${wsProtocol}//${window.location.host}/ws/rooms/${roomId}?token=${state.token}`;
        
        state.ws = new WebSocket(wsUrl);

        state.ws.onopen = () => console.log('WebSocket connection established.');
        state.ws.onclose = () => console.log('WebSocket connection closed.');
        state.ws.onerror = (error) => console.error('WebSocket error:', error);
        state.ws.onmessage = (event) => handleWsMessage(JSON.parse(event.data));
    }

    function sendWsMessage(message) {
        if (state.ws && state.ws.readyState === WebSocket.OPEN) {
            state.ws.send(JSON.stringify(message));
        }
    }

    function handleWsMessage(message) {
        console.log('WS Message Received:', message);
        const video = document.getElementById('video-player');

        switch (message.type) {
            case 'update_state':
                const serverState = message.data;
                // Avoid seeking if the time difference is small to prevent stuttering
                if (Math.abs(video.currentTime - serverState.current_time) > 1.5) {
                    video.currentTime = serverState.current_time;
                }
                serverState.is_playing ? video.play() : video.pause();
                break;
            case 'user_joined':
            case 'user_left':
            case 'update_user_list':
                updateUserList(message.data.users);
                break;
            case 'chat_message':
                displayChatMessage(message.data);
                break;
        }
    }
    
    function updateUserList(users) {
        const userListEl = document.getElementById('user-list-room');
        userListEl.innerHTML = users.map(user => `<li>${user.username}</li>`).join('');
    }

    function displayChatMessage(message) {
        const chatMessages = document.getElementById('chat-messages');
        const messageEl = document.createElement('div');
        messageEl.innerHTML = `<span class="font-bold">${message.username}:</span> ${message.content}`;
        chatMessages.appendChild(messageEl);
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }

    // --- EVENT LISTENERS ---
    function setupEventListeners() {
        // Auth
        loginForm.addEventListener('submit', handleLogin);
        registerForm.addEventListener('submit', handleRegister);
        showRegisterLink.addEventListener('click', (e) => { e.preventDefault(); showRegister(); });
        showLoginLink.addEventListener('click', (e) => { e.preventDefault(); showLogin(); });
        document.getElementById('logout-pending').addEventListener('click', logout);
        document.getElementById('logout-admin').addEventListener('click', logout);
        document.getElementById('logout-library').addEventListener('click', logout);

        // Admin
        document.getElementById('admin-view').addEventListener('click', async (e) => {
            const target = e.target;
            const userId = target.dataset.userId;
            if (!userId) return;

            try {
                if (target.classList.contains('approve-btn')) {
                    await apiRequest(`/api/admin/users/${userId}/approve`, 'POST');
                } else if (target.classList.contains('deny-btn')) {
                    await apiRequest(`/api/admin/users/${userId}/deny`, 'POST');
                }
                renderAdminView(); // Refresh list
            } catch (error) {
                alert(`Failed to update user: ${error.message}`);
            }
        });

        // Library
        document.getElementById('video-grid').addEventListener('click', (e) => {
            const videoCard = e.target.closest('[data-video-id]');
            if (videoCard) {
                createRoom(videoCard.dataset.videoId);
            }
        });
        document.getElementById('join-room-btn').addEventListener('click', () => {
            const roomId = document.getElementById('join-room-id').value;
            if (roomId) {
                navigate('room', { id: roomId });
            }
        });

        // Room
        document.getElementById('leave-room-btn').addEventListener('click', leaveRoom);
        document.getElementById('chat-form').addEventListener('submit', (e) => {
            e.preventDefault();
            const chatInput = document.getElementById('chat-input');
            const content = chatInput.value.trim();
            if (content) {
                sendWsMessage({ type: 'chat_message', data: { content } });
                chatInput.value = '';
            }
        });
    }

    // --- INITIALIZATION ---
    async function initializeApp() {
        if (state.token) {
            await fetchCurrentUser();
            if (state.user) {
                if (state.user.is_admin) {
                    navigate('admin');
                } else if (!state.user.is_verified) {
                    navigate('pending');
                } else {
                    // Handle hash-based routing on page load
                    const hash = window.location.hash.slice(1);
                    const [view, id] = hash.split('/');
                    if (view === 'room' && id) {
                        navigate('room', { id });
                    } else {
                        navigate('library');
                    }
                }
            } else {
                // Token is invalid/expired
                logout();
            }
        } else {
            navigate('auth');
        }
    }

    setupEventListeners();
    initializeApp();
});