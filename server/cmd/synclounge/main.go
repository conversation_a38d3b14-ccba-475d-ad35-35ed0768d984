 󰜥  ./synclounge                   
2025/06/28 20:03:22 Starting SyncLounge server...
[GIN-debug] [WARNING] Creating an Engine instance with the Logger and Recovery middleware already attached.

[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
 - using env:   export GIN_MODE=release
 - using code:  gin.SetMode(gin.ReleaseMode)

[GIN-debug] GET    /static/*filepath         --> github.com/gin-gonic/gin.(*RouterGroup).createStaticHandler.func1 (3 handlers)
[GIN-debug] HEAD   /static/*filepath         --> github.com/gin-gonic/gin.(*RouterGroup).createStaticHandler.func1 (3 handlers)
[GIN-debug] Loaded HTML Templates (2): 
        - 
        - index.html.tmpl

[GIN-debug] GET    /                         --> synclounge/server/internal/api/handlers.(*RoomHandler).ServeIndexHandler-fm (3 handlers)
[GIN-debug] GET    /login                    --> synclounge/server/internal/api/handlers.(*RoomHandler).ServeIndexHandler-fm (3 handlers)
[GIN-debug] GET    /register                 --> synclounge/server/internal/api/handlers.(*RoomHandler).ServeIndexHandler-fm (3 handlers)
[GIN-debug] GET    /rooms/:roomID            --> synclounge/server/internal/api/handlers.(*RoomHandler).ServeIndexHandler-fm (3 handlers)
[GIN-debug] GET    /ws/rooms/:roomID         --> synclounge/server/internal/api/handlers.(*RoomHandler).JoinRoomHandler-fm (3 handlers)
[GIN-debug] POST   /register                 --> synclounge/server/internal/api/handlers.(*UserHandler).RegisterUser-fm (3 handlers)
[GIN-debug] POST   /login                    --> synclounge/server/internal/api/handlers.(*UserHandler).LoginUser-fm (3 handlers)
[GIN-debug] GET    /api/library              --> synclounge/server/internal/api/handlers.(*VideoHandler).GetMediaLibrary-fm (4 handlers)
[GIN-debug] POST   /api/rooms                --> synclounge/server/internal/api/handlers.(*RoomHandler).CreateRoomHandler-fm (4 handlers)
[GIN-debug] GET    /api/rooms/:roomId        --> synclounge/server/internal/api/handlers.(*RoomHandler).GetRoomHandler-fm (4 handlers)
[GIN-debug] GET    /api/users/me             --> synclounge/server/internal/api/handlers.(*UserHandler).GetCurrentUser-fm (4 handlers)
2025/06/28 20:03:22 Scanning media library in: ./media
2025/06/28 20:03:22 Media library scan complete. Found 12 videos.
[GIN-debug] GET    /api/stream/:videoID/playlist.m3u8 --> synclounge/server/internal/api/handlers.(*VideoHandler).StreamVideoHandler-fm (4 handlers)
[GIN-debug] GET    /api/stream/:videoID/:segment --> synclounge/server/internal/api/handlers.(*VideoHandler).ServeHlsSegment-fm (4 handlers)
[GIN-debug] GET    /api/admin/users          --> synclounge/server/internal/api/handlers.(*UserHandler).GetUsers-fm (5 handlers)
[GIN-debug] POST   /api/admin/users/:userID/approve --> synclounge/server/internal/api/handlers.(*UserHandler).ApproveUser-fm (5 handlers)
[GIN-debug] DELETE /api/admin/users/:userID  --> synclounge/server/internal/api/handlers.(*UserHandler).DenyUser-fm (5 handlers)
2025/06/28 20:03:22 Server is listening on port 8080
[GIN-debug] [WARNING] You trusted all proxies, this is NOT safe. We recommend you to set a value.
Please check https://pkg.go.dev/github.com/gin-gonic/gin#readme-don-t-trust-all-proxies for details.
[GIN-debug] Listening and serving HTTP on :8080
[GIN] 2025/06/28 - 20:03:24 | 200 |     188.209µs |       127.0.0.1 | GET      "/"
[GIN] 2025/06/28 - 20:03:24 | 304 |       30.31µs |       127.0.0.1 | GET      "/static/js/app.js"
[GIN] 2025/06/28 - 20:03:24 | 200 |     126.529µs |       127.0.0.1 | GET      "/api/users/me"
[GIN] 2025/06/28 - 20:03:24 | 200 |      45.829µs |       127.0.0.1 | GET      "/api/library"
[GIN] 2025/06/28 - 20:03:25 | 200 |       93.82µs |       127.0.0.1 | POST     "/api/rooms"
[GIN] 2025/06/28 - 20:03:25 | 200 |       36.77µs |       127.0.0.1 | GET      "/api/rooms/9e98282ff8acd1be421ae7ee81199e89"
[GIN] 2025/06/28 - 20:03:25 | 200 |       34.53µs |       127.0.0.1 | GET      "/ws/rooms/9e98282ff8acd1be421ae7ee81199e89?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJyb2xlIjoiYWRtaW4iLCJleHAiOjE3NTEyNDg5ODV9.Ozq-U4fiq41HLWmHY7jWUYOH_gfWuegRNTWfsnN1cPk"
[GIN] 2025/06/28 - 20:03:25 | 404 |         170ns |       127.0.0.1 | GET      "/hls/e8ea95bf5bb23958f987e2b95b446cca16b996d0/master.m3u8"
2025/06/28 20:03:38 error: websocket: close 1005 (no status)
[GIN] 2025/06/28 - 20:03:38 | 200 |       44.73µs |       127.0.0.1 | GET      "/api/library"
^Cpackage main

import (
	"flag"
	"log"

	"synclounge/server/internal/api/handlers"
	"synclounge/server/internal/api/middleware"
	"synclounge/server/internal/core/services"
	"synclounge/server/internal/core/websocket"
	"synclounge/server/internal/database"

	"github.com/gin-gonic/gin"
)

var mediaDir = flag.String("media-dir", "./media", "the directory containing media files")

func main() {
	flag.Parse()
	log.Println("Starting SyncLounge server...")

	// Initialize the database
	database.InitDB("synclounge.db")
	db := database.DB

	// Create the user service and handler
	userService := services.NewUserService(db)
	userHandler := handlers.NewUserHandler(userService)

	// Create the video service and handler
	videoService := services.NewVideoService()
	videoHandler := handlers.NewVideoHandler(videoService)

	// Scan the media library in the background
	go videoService.ScanLibrary(*mediaDir)

	// Create a new Gin router
	router := gin.Default()

	// Initialize the websocket hub
	hub := websocket.NewHub()
	go hub.Run()

	// Create the room service and handler
	roomService := services.NewRoomService(hub, videoService)
	roomHandler := handlers.NewRoomHandler(roomService, hub)

	// Serve static files and templates
	router.Static("/static", "./web/static")
	router.LoadHTMLGlob("web/templates/*")

	// Frontend routes
	router.GET("/", roomHandler.ServeIndexHandler)
	router.GET("/login", roomHandler.ServeIndexHandler)
	router.GET("/register", roomHandler.ServeIndexHandler)
	router.GET("/rooms/:roomID", roomHandler.ServeIndexHandler)

	// Websocket route
	router.GET("/ws/rooms/:roomID", roomHandler.JoinRoomHandler)

	// Public routes
	router.POST("/register", userHandler.RegisterUser)
	router.POST("/login", userHandler.LoginUser)

	// Authenticated routes
	apiRoutes := router.Group("/api")
	apiRoutes.Use(middleware.JWTAuthMiddleware())
	{
		apiRoutes.GET("/library", videoHandler.GetMediaLibrary)
		apiRoutes.POST("/rooms", roomHandler.CreateRoomHandler)
		apiRoutes.GET("/rooms/:roomId", roomHandler.GetRoomHandler)
		apiRoutes.GET("/users/me", userHandler.GetCurrentUser)

		// HLS streaming routes
		apiRoutes.GET("/stream/:videoID/playlist.m3u8", videoHandler.StreamVideoHandler)
		apiRoutes.GET("/stream/:videoID/:segment", videoHandler.ServeHlsSegment)
	}

	// Admin routes
	adminRoutes := router.Group("/api/admin")
	adminRoutes.Use(middleware.JWTAuthMiddleware())
	adminRoutes.Use(middleware.RequireAdmin())
	{
		adminRoutes.GET("/users", userHandler.GetUsers)
		adminRoutes.POST("/users/:userID/approve", userHandler.ApproveUser)
		adminRoutes.DELETE("/users/:userID", userHandler.DenyUser)
	}

	// Start the server
	log.Println("Server is listening on port 8080")
	if err := router.Run(":8080"); err != nil {
		log.Fatalf("Could not start server: %s\n", err)
	}
}
